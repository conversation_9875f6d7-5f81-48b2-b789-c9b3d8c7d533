import 'dart:convert';
import 'dart:io';
import 'package:bloodplusmobile/core/config/api_config.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/data/models/appointment_model.dart';
import 'package:bloodplusmobile/data/repositories/appointment_response.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

class AppointmentService {
  static final HttpClient _httpClient = HttpClient()
    ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  static final client = IOClient(_httpClient);
  final UserManager _userManager = UserManager();

  // Create a separate HTTP client for multipart requests
  static HttpClient get _multipartHttpClient {
    final client = HttpClient();
    client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return client;
  }

  // Tạo cuộc hẹn
  Future<void> createAppointment(Map<String, dynamic> payload) async {
    final token = await _userManager.getUserToken();
    final url = Uri.parse(ApiConfig.getFullUrl('/appointment'));

    try {
      final response = await client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(payload),
      );

      if (response.statusCode == 200) {
        // Appointment created successfully
      } else {
        throw Exception('Tạo cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  // Lấy danh sách cuộc hẹn của người dùng
  Future<List<Appointment>> getAppointments({
    int pageNumber = 1,
    int pageSize = 5,
    bool forceRefresh = false,
  }) async {
    final token = await _userManager.getUserToken();
    final queryParameters = {
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
    };

    final url = Uri.parse(ApiConfig.getFullUrl('/appointment')).replace(queryParameters: queryParameters);

    try {
      print('=== GET APPOINTMENTS REQUEST ===');
      print('URL: $url');
      print('Token: ${token != null ? 'exists' : 'null'}');

      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
          if (forceRefresh) 'Cache-Control': 'no-cache',
        },
      );

      print('=== GET APPOINTMENTS RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final appointmentResponse = AppointmentResponse.fromJson(data);
        print('AppointmentService: Successfully loaded ${appointmentResponse.items.length} appointments');
        return appointmentResponse.items;
      } else {
        throw Exception('Lấy danh sách cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('AppointmentService error: $e');
      throw Exception('Lỗi kết nối: $e');
    }
  }

  // Hủy cuộc hẹn
  Future<void> cancelAppointment(String appointmentId) async {
    final token = await _userManager.getUserToken();
    final url = Uri.parse(ApiConfig.getFullUrl('/appointment/markcancel-$appointmentId'));

    try {
      final response = await client.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        // Appointment canceled successfully
      } else {
        throw Exception('Hủy cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<void> MarkcompleteAppointment(String appointmentId, {File? certificationImage}) async {
    final token = await _userManager.getUserToken();
    final url = Uri.parse(ApiConfig.getFullUrl('/appointment/markneedconfirm'));

    try {
      // Use IOClient with custom HttpClient for SSL bypass
      final httpClient = _multipartHttpClient;
      final ioClient = IOClient(httpClient);

      var request = http.MultipartRequest('PATCH', url);

      // Add headers
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': '*/*',
      });

      // Add appointment ID as query parameter
      request.fields['id'] = appointmentId;

      // Add certification image if provided
      if (certificationImage != null) {
        var multipartFile = await http.MultipartFile.fromPath(
          'Certification',
          certificationImage.path,
        );
        request.files.add(multipartFile);
      }

      final streamedResponse = await ioClient.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      ioClient.close();

      if (response.statusCode == 200) {
        // Appointment completed successfully
      } else {
        throw Exception('Hoàn thành cuộc hẹn thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }
}