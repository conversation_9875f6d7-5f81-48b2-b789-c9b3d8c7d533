import 'package:bloodplusmobile/core/language_helper/language_manager.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/presentation/features/onboarding/splash/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'core/language_helper/localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'dart:io'; // Thêm để sử dụng HttpOverrides

// Tùy chỉnh HttpOverrides để bỏ qua lỗi chứng chỉ (tạm thời)
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

Future<void> main() async {
  // Firebase init
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Áp dụng HttpOverrides toàn cục
  HttpOverrides.global = MyHttpOverrides();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LanguageManager()..loadLanguage()),
        ChangeNotifierProvider(create: (context) => AppStateNotifier()),
      ],
      child: const BloodDonationApp(),
    ),
  );
}

class BloodDonationApp extends StatelessWidget {
  const BloodDonationApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return MaterialApp(
          title: 'Mau Cong',
          theme: ThemeData(
            primarySwatch: Colors.red,
          ),
          home: const SplashScreen(),
          debugShowCheckedModeBanner: false,
          locale: languageManager.locale,
          supportedLocales: AppLocalizations.supportedLocales,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
        );
      },
    );
  }
}