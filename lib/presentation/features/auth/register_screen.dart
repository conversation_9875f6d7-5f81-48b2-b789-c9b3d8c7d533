import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:bloodplusmobile/core/config/api_config.dart' as api_config;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  _RegisterScreenState createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _verifyPasswordController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nameController = TextEditingController();
  final _dateOfBirthController = TextEditingController();
  final _addressController = TextEditingController();
  final _jobController = TextEditingController();
  final _passportNumberController = TextEditingController();
  final _otpController = TextEditingController();
  String? _verificationId;
  bool _isOtpSent = false;
  File? _userImage;
  int? _gender = 0;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _verifyPasswordController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nameController.dispose();
    _dateOfBirthController.dispose();
    _addressController.dispose();
    _jobController.dispose();
    _passportNumberController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _sendOtp() async {
    String phone = _phoneController.text.trim();

    if (phone.length != 10 || !_isNumeric(phone) || !phone.startsWith('0')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Số điện thoại phải gồm 10 chữ số và bắt đầu bằng 0.')),
      );
      return;
    }

    try {
      String formattedPhone = '+84${phone.substring(1)}';

      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: formattedPhone,
        timeout: const Duration(seconds: 60),
        verificationCompleted: (PhoneAuthCredential credential) async {
          await FirebaseAuth.instance.signInWithCredential(credential);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Xác minh tự động thành công!')),
          );
        },
        verificationFailed: (FirebaseAuthException e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Lỗi xác minh: ${e.message ?? "Không xác định"}')),
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          setState(() {
            _verificationId = verificationId;
            _isOtpSent = true;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Mã OTP đã được gửi!')),
          );
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          setState(() {
            _verificationId = verificationId;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Hết thời gian tự động nhận OTP.')),
          );
        },
      );
    } catch (e) {
      print('Send OTP Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi gửi OTP: $e')),
      );
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _userImage = File(image.path);
      });
    }
  }

  Future<void> _verifyOtpAndRegister() async {
    if (_formKey.currentState!.validate()) {
      if (_passwordController.text != _verifyPasswordController.text) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Mật khẩu và xác nhận mật khẩu không khớp.')),
        );
        return;
      }

      try {
        PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: _verificationId!,
          smsCode: _otpController.text,
        );

        try {
          await FirebaseAuth.instance.signInWithCredential(credential);
        } catch (e) {
          if (FirebaseAuth.instance.currentUser == null) {
            throw Exception('Không thể xác thực người dùng.');
          }
        }

        String idToken = await FirebaseAuth.instance.currentUser?.getIdToken() ?? '';
        if (idToken.isEmpty) {
          throw Exception('Không thể lấy IdToken.');
        }

        HttpClient client = HttpClient();
        client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;

        var request = http.MultipartRequest(
          'POST',
          Uri.parse(api_config.ApiConfig.getFullUrl(api_config.ApiConfig.authRegister)),
        );

        request.fields['UserName'] = _usernameController.text;
        request.fields['Password'] = _passwordController.text;
        request.fields['VerifyPassword'] = _verifyPasswordController.text;
        request.fields['Email'] = _emailController.text;
        request.fields['PhoneNumber'] = _phoneController.text; // Giữ nguyên số 0 đầu
        request.fields['BloodType'] = '';
        request.fields['Name'] = _nameController.text;
        request.fields['DateOfBirth'] = _dateOfBirthController.text;
        request.fields['Address'] = _addressController.text;
        request.fields['Job'] = _jobController.text;
        request.fields['Gender'] = _gender.toString();
        request.fields['PassportNumber'] = _passportNumberController.text;
        request.fields['IdToken'] = idToken;

        if (_userImage != null) {
          request.files.add(await http.MultipartFile.fromPath(
            'UserImage',
            _userImage!.path,
            contentType: MediaType('image', '*'),
          ));
        }

        var response = await request.send();
        var responseBody = await http.Response.fromStream(response);

        if (response.statusCode == 200) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Đăng ký thành công!')),
          );
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Đăng ký thất bại: ${responseBody.body}')),
          );
        }
      } catch (e, stackTrace) {
        print('Error: $e');
        print('StackTrace: $stackTrace');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi: $e')),
        );
      }
    }
  }

  bool _isNumeric(String s) {
    return double.tryParse(s) != null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Đăng Ký')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(labelText: 'Tên đăng nhập'),
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Vui lòng nhập tên đăng nhập';
                  return null;
                },
              ),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(labelText: 'Mật khẩu'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.length < 6) return 'Mật khẩu phải ít nhất 6 ký tự';
                  return null;
                },
              ),
              TextFormField(
                controller: _verifyPasswordController,
                decoration: const InputDecoration(labelText: 'Xác nhận mật khẩu'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Vui lòng xác nhận mật khẩu';
                  return null;
                },
              ),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'Email'),
                validator: (value) {
                  if (value == null || value.isEmpty || !value.contains('@')) return 'Email không hợp lệ';
                  return null;
                },
              ),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(labelText: 'Số điện thoại (10 số, bắt đầu bằng 0)'),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.length != 10 || !_isNumeric(value) || !value.startsWith('0')) {
                    return 'Số điện thoại phải gồm 10 chữ số và bắt đầu bằng số 0';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Họ và tên'),
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Vui lòng nhập họ và tên';
                  return null;
                },
              ),
              TextFormField(
                controller: _dateOfBirthController,
                decoration: const InputDecoration(labelText: 'Ngày sinh (YYYY-MM-DD)'),
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Vui lòng nhập ngày sinh';
                  return null;
                },
              ),
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(labelText: 'Địa chỉ'),
              ),
              TextFormField(
                controller: _jobController,
                decoration: const InputDecoration(labelText: 'Công việc'),
              ),
              DropdownButtonFormField<int>(
                value: _gender,
                decoration: const InputDecoration(labelText: 'Giới tính'),
                items: const [
                  DropdownMenuItem(value: 0, child: Text('Nam')),
                  DropdownMenuItem(value: 1, child: Text('Nữ')),
                  DropdownMenuItem(value: 2, child: Text('Khác')),
                ],
                onChanged: (value) {
                  setState(() {
                    _gender = value;
                  });
                },
                validator: (value) {
                  if (value == null) return 'Vui lòng chọn giới tính';
                  return null;
                },
              ),
              TextFormField(
                controller: _passportNumberController,
                decoration: const InputDecoration(labelText: 'Số passport'),
              ),
              ElevatedButton(
                onPressed: _pickImage,
                child: const Text('Chọn ảnh đại diện'),
              ),
              if (_userImage != null) Text('Ảnh đã chọn: ${_userImage!.path.split('/').last}'),
              const SizedBox(height: 20),
              if (!_isOtpSent)
                ElevatedButton(
                  onPressed: _sendOtp,
                  child: const Text('Gửi OTP'),
                ),
              if (_isOtpSent)
                TextFormField(
                  controller: _otpController,
                  decoration: const InputDecoration(labelText: 'Nhập mã OTP'),
                  validator: (value) {
                    if (value == null || value.isEmpty) return 'Vui lòng nhập mã OTP';
                    return null;
                  },
                ),
              if (_isOtpSent)
                ElevatedButton(
                  onPressed: _verifyOtpAndRegister,
                  child: const Text('Xác nhận và Đăng ký'),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
