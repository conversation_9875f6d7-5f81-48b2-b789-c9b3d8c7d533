import 'package:bloodplusmobile/core/constants/app_colors.dart';
import 'package:bloodplusmobile/core/widgets/custom_button_navBar.dart';
import 'package:bloodplusmobile/core/widgets/feature_grid.dart';
import 'package:bloodplusmobile/core/widgets/header_section.dart';
import 'package:bloodplusmobile/core/widgets/news_carousel.dart';
import 'package:bloodplusmobile/data/manager/app_state_notifier.dart';
import 'package:bloodplusmobile/data/manager/user_manager.dart';
import 'package:bloodplusmobile/presentation/features/days_waiting/days_waiting_screen.dart';
import 'package:bloodplusmobile/presentation/features/schedule/donation_history_screen.dart';
import 'package:bloodplusmobile/presentation/features/user/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final List<Map<String, dynamic>> features = [
    {'icon': 'assets/icons/schedule.png', 'title': 'schedule_donation', 'color': Colors.red},
    {'icon': 'assets/icons/emergency.png', 'title': 'emergency_donation', 'color': Colors.red},
    {'icon': 'assets/icons/nearly_hospital.png', 'title': 'nearby_hospitals', 'color': Colors.red},
    {'icon': 'assets/icons/news.png', 'title': 'blog_list', 'color': Colors.red},
    {'icon': 'assets/icons/doctor.png', 'title': 'expert_advice', 'color': Colors.red},
    {'icon': 'assets/icons/information.png', 'title': 'information', 'color': Colors.red},
    {'icon': 'assets/icons/logo_white.png', 'title': 'leaderboard', 'color': Colors.red},
    {'icon': 'assets/icons/logo_white.png', 'title': 'voucher', 'color': Colors.red},
    {'icon': 'assets/icons/logo_white.png', 'title': 'shopping', 'color': Colors.red},
  ];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _fadeController.forward();

    // Ensure all data is loaded when home screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
      appStateNotifier.fetchAllData(forceRefresh: true);
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() => _selectedIndex = index);
    if (index == 4) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ProfileScreen(),
        ),
      );
    }
    if (index == 3 ) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const HistoryScreen(),
        ),
      );
    }
    if (index == 1 ) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const DaysWaitingScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: AppColors.primaryRed,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
      child: Scaffold(
        backgroundColor: const Color(0xFFF8F9FA),
        body: SafeArea(
          top: false,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: RefreshIndicator(
              onRefresh: () async {
                final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
                await appStateNotifier.fetchAllData(forceRefresh: true);
              },
              color: AppColors.primaryRed,
              child: ListView(
                padding: EdgeInsets.zero,
                physics: const BouncingScrollPhysics(),
                children: [
                  HeaderSection(),
                  const SizedBox(height: 8),
                  NewsCarousel(),
                  const SizedBox(height: 16),
                  FeatureGrid(features: features),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: CustomBottomNavBar(
          selectedIndex: _selectedIndex,
          onItemTapped: _onItemTapped,
        ),
      ),
    );
  }
}